package com.flutterup.players.compose

import android.content.Context
import androidx.compose.runtime.*
import com.flutterup.players.impl.AlphaExoPlayerImpl
import com.flutterup.players.view.AlphaExoPlayerView
import com.ss.ugc.android.alpha_player.IMonitor
import com.ss.ugc.android.alpha_player.IPlayerAction
import com.ss.ugc.android.alpha_player.model.ScaleType
import com.ss.ugc.android.alpha_player.player.AbsPlayer
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * AlphaPlayer 状态管理类
 * 统一管理播放器的所有状态和属性
 */
@Stable
class AlphaPlayerState {
    
    // 播放器视图引用
    private var playerView: AlphaExoPlayerView? = null
    
    // 视频源状态
    private val _videoSource = MutableStateFlow<String?>(null)
    val videoSource: StateFlow<String?> = _videoSource.asStateFlow()
    
    // 播放状态
    private val _isPlaying = MutableStateFlow(false)
    val isPlaying: StateFlow<Boolean> = _isPlaying.asStateFlow()
    
    private val _isReady = MutableStateFlow(false)
    val isReady: StateFlow<Boolean> = _isReady.asStateFlow()
    
    private val _isCompleted = MutableStateFlow(false)
    val isCompleted: StateFlow<Boolean> = _isCompleted.asStateFlow()
    
    // 配置状态
    private val _autoPlay = MutableStateFlow(true)
    val autoPlay: StateFlow<Boolean> = _autoPlay.asStateFlow()
    
    private val _looping = MutableStateFlow(false)
    val looping: StateFlow<Boolean> = _looping.asStateFlow()
    
    private val _scaleType = MutableStateFlow(ScaleType.ScaleAspectFitCenter)
    val scaleType: StateFlow<ScaleType> = _scaleType.asStateFlow()
    
    // 错误状态
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    // Compose 状态（用于触发重组）
    var isReadyCompose by mutableStateOf(false)
        private set

    var isCompletedCompose by mutableStateOf(false)
        private set

    var errorMessageCompose by mutableStateOf<String?>(null)
        private set

    var isPlayingCompose by mutableStateOf(false)
        private set
    
    /**
     * 设置视频源
     */
    fun setVideoSource(path: String) {
        _videoSource.value = path
        _isCompleted.value = false
        isCompletedCompose = false
        _errorMessage.value = null
        errorMessageCompose = null
        
        // 如果播放器视图已经绑定，立即播放
        playerView?.let { view ->
            if (path.isNotEmpty()) {
                view.startVideo(path)
            }
        }
    }
    
    /**
     * 设置自动播放
     */
    fun setAutoPlay(auto: Boolean) {
        _autoPlay.value = auto
    }
    
    /**
     * 设置循环播放
     */
    fun setLooping(loop: Boolean) {
        _looping.value = loop
    }
    
    /**
     * 设置缩放类型
     */
    fun setScaleType(scale: ScaleType) {
        _scaleType.value = scale
        playerView?.setScaleType(scale)
    }
    
    /**
     * 播放
     */
    fun play() {
        playerView?.let { view ->
            _videoSource.value?.let { path ->
                if (path.isNotEmpty()) {
                    view.startVideo(path)
                    _isPlaying.value = true
                    isPlayingCompose = true
                }
            }
        }
    }
    
    /**
     * 停止播放
     */
    fun stop() {
        _isPlaying.value = false
        isPlayingCompose = false
        _isCompleted.value = true
        isCompletedCompose = true
    }
    
    /**
     * 重新播放
     */
    fun replay() {
        _isCompleted.value = false
        isCompletedCompose = false
        play()
    }
    
    /**
     * 创建播放器实例
     */
    fun createPlayer(context: Context): AbsPlayer {
        return AlphaExoPlayerImpl(context)
    }
    
    /**
     * 绑定到播放器视图
     */
    fun bindToView(view: AlphaExoPlayerView) {
        this.playerView = view

        // 设置播放器监听器
        setupPlayerListeners(view)

        // 如果已有视频源，立即播放
        _videoSource.value?.let { path ->
            if (path.isNotEmpty() && _autoPlay.value) {
                view.startVideo(path)
            }
        }
    }

    /**
     * 设置播放器监听器
     */
    private fun setupPlayerListeners(view: AlphaExoPlayerView) {
        // 设置播放器动作监听
        view.setPlayerAction(object : IPlayerAction {
            override fun startAction() {
                _isPlaying.value = true
                isPlayingCompose = true
                _isReady.value = true
                isReadyCompose = true
            }

            override fun endAction() {
                _isPlaying.value = false
                isPlayingCompose = false
                _isCompleted.value = true
                isCompletedCompose = true

                // 如果设置了循环播放，重新播放
                if (_looping.value) {
                    replay()
                }
            }

            override fun onVideoSizeChanged(videoWidth: Int, videoHeight: Int, scaleType: ScaleType) {
                // 视频尺寸变化处理
            }
        })

        // 设置监控器
        view.setMonitor(object : IMonitor {
            override fun monitor(result: Boolean, playType: String, what: Int, extra: Int, errorInfo: String) {
                if (!result) {
                    setError(errorInfo)
                }
            }
        })
    }
    
    /**
     * 更新视图状态
     */
    fun updateView(view: AlphaExoPlayerView) {
        // 当状态发生变化时，更新视图
        // 这里可以根据需要添加更多的状态同步逻辑
    }
    
    /**
     * 生命周期：开始
     */
    fun onStart() {
        playerView?.attachView()
    }
    
    /**
     * 生命周期：停止
     */
    fun onStop() {
        playerView?.detachView()
    }
    
    /**
     * 生命周期：销毁
     */
    fun onDestroy() {
        playerView?.releasePlayerController()
        playerView = null
        
        // 重置状态
        _isPlaying.value = false
        _isReady.value = false
        _isCompleted.value = false
        _errorMessage.value = null

        isPlayingCompose = false
        isReadyCompose = false
        isCompletedCompose = false
        errorMessageCompose = null
    }
    
    /**
     * 设置播放器准备完成状态
     */
    internal fun setReady(ready: Boolean) {
        _isReady.value = ready
        isReadyCompose = ready
    }
    
    /**
     * 设置播放完成状态
     */
    internal fun setCompleted(completed: Boolean) {
        _isCompleted.value = completed
        isCompletedCompose = completed
        if (completed) {
            _isPlaying.value = false
            isPlayingCompose = false

            // 如果设置了循环播放，重新播放
            if (_looping.value) {
                replay()
            }
        }
    }
    
    /**
     * 设置错误信息
     */
    internal fun setError(error: String?) {
        _errorMessage.value = error
        errorMessageCompose = error
        if (error != null) {
            _isPlaying.value = false
            isPlayingCompose = false
        }
    }
}

/**
 * 记住 AlphaPlayerState 实例
 */
@Composable
fun rememberAlphaPlayerState(): AlphaPlayerState {
    return remember { AlphaPlayerState() }
}
