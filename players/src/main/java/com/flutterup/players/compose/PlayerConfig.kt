package com.flutterup.players.compose

import com.ss.ugc.android.alpha_player.model.AlphaVideoViewType
import com.ss.ugc.android.alpha_player.model.ScaleType

/**
 * 播放器配置类
 * 用于配置播放器的各种参数
 */
data class PlayerConfig(
    
    /**
     * 是否循环播放
     */
    val looping: Boolean = false,
    
    /**
     * 视频缩放类型
     */
    val scaleType: ScaleType = ScaleType.ScaleAspectFitCenter,
    
    /**
     * 视频视图类型
     */
    val videoViewType: AlphaVideoViewType = AlphaVideoViewType.GL_TEXTURE_VIEW,
    
    /**
     * 是否启用日志
     */
    val enableLogging: Boolean = false
)

/**
 * 播放器配置构建器
 */
class PlayerConfigBuilder {
    private var autoPlay: Boolean = true
    private var looping: Boolean = false
    private var scaleType: ScaleType = ScaleType.ScaleAspectFitCenter
    private var videoViewType: AlphaVideoViewType = AlphaVideoViewType.GL_TEXTURE_VIEW
    private var enableLogging: Boolean = false
    
    /**
     * 设置自动播放
     */
    fun autoPlay(auto: <PERSON>olean) = apply {
        this.autoPlay = auto
    }
    
    /**
     * 设置循环播放
     */
    fun looping(loop: Boolean) = apply {
        this.looping = loop
    }
    
    /**
     * 设置缩放类型
     */
    fun scaleType(scale: ScaleType) = apply {
        this.scaleType = scale
    }
    
    /**
     * 设置视频视图类型
     */
    fun videoViewType(type: AlphaVideoViewType) = apply {
        this.videoViewType = type
    }
    
    /**
     * 设置是否启用日志
     */
    fun enableLogging(enable: Boolean) = apply {
        this.enableLogging = enable
    }
    
    /**
     * 构建配置
     */
    fun build(): PlayerConfig {
        return PlayerConfig(
            looping = looping,
            scaleType = scaleType,
            videoViewType = videoViewType,
            enableLogging = enableLogging
        )
    }
}

/**
 * 创建播放器配置的 DSL 函数
 */
fun playerConfig(builder: PlayerConfigBuilder.() -> Unit): PlayerConfig {
    return PlayerConfigBuilder().apply(builder).build()
}

/**
 * 预设配置
 */
object PlayerConfigPresets {
    
    /**
     * 默认配置
     */
    val Default = PlayerConfig()
    
    /**
     * 自动播放循环配置
     */
    val AutoPlayLooping = PlayerConfig(
        looping = true
    )
    
    /**
     * 手动播放配置
     */
    val ManualPlay = PlayerConfig(
        looping = false
    )
    
    /**
     * 高性能配置（使用 GLSurfaceView）
     */
    val HighPerformance = PlayerConfig(
        looping = false,
        videoViewType = AlphaVideoViewType.GL_SURFACE_VIEW
    )
    
    /**
     * 自定义显示层配置（使用 GLTextureView）
     */
    val CustomDisplay = PlayerConfig(
        looping = false,
        videoViewType = AlphaVideoViewType.GL_TEXTURE_VIEW
    )
    
    /**
     * 调试配置
     */
    val Debug = PlayerConfig(
        looping = false,
        enableLogging = true
    )
}
