package com.flutterup.players.view

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.lifecycle.LifecycleOwner
import com.flutterup.players.databinding.ViewAlphaExoPlayerBinding
import com.flutterup.players.impl.AlphaExoPlayerImpl
import com.ss.ugc.android.alpha_player.IMonitor
import com.ss.ugc.android.alpha_player.IPlayerAction
import com.ss.ugc.android.alpha_player.controller.IPlayerController
import com.ss.ugc.android.alpha_player.controller.PlayerController
import com.ss.ugc.android.alpha_player.model.AlphaVideoViewType
import com.ss.ugc.android.alpha_player.model.Configuration
import com.ss.ugc.android.alpha_player.model.DataSource
import com.ss.ugc.android.alpha_player.model.ScaleType
import com.ss.ugc.android.alpha_player.player.AbsPlayer

class AlphaExoPlayerView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {

    private val binding = ViewAlphaExoPlayerBinding.inflate(LayoutInflater.from(context), this, true)

    private var controller: IPlayerController? = null

    private var playerImpl: AbsPlayer? = null

    private var scaleType: ScaleType = ScaleType.ScaleAspectFitCenter

    /**
     * 初始化播放器控制器
     * @param context 上下文
     * @param owner 生命周期所有者
     * @param alphaVideoViewType 视频视图类型
     */
    fun initPlayerController(
        context: Context,
        owner: LifecycleOwner,
        player: AbsPlayer = AlphaExoPlayerImpl(context),
        alphaVideoViewType: AlphaVideoViewType = AlphaVideoViewType.GL_TEXTURE_VIEW
    ) {
        val configuration = Configuration(context, owner)
        //  GLTextureView supports custom display layer, but GLSurfaceView has better performance, and the GLSurfaceView is default.
        configuration.alphaVideoViewType = alphaVideoViewType
        //  You can implement your IMediaPlayer, here we use ExoPlayerImpl that implemented by ExoPlayer, and
        //  we support DefaultSystemPlayer as default player.
        playerImpl = player

        controller = PlayerController.Companion.get(configuration, playerImpl)
    }

    fun setPlayerAction(playerAction: IPlayerAction) {
        controller?.setPlayerAction(playerAction)
    }

    fun setMonitor(monitor: IMonitor) {
        controller?.setMonitor(monitor)
    }

    fun startVideo(filePath: String) {
        if (TextUtils.isEmpty(filePath)) {
            return
        }

        var removedPath = filePath

        val cacheDir = context.cacheDir

        if (filePath.contains(cacheDir.path)) {
            removedPath = removedPath.substring(cacheDir.path.length + 1) // 移除缓存目录路径
        }

        val dataSource = DataSource()
            .setBaseDir(cacheDir.path)
            .setPortraitPath(removedPath, scaleType.ordinal)
            .setLandscapePath(removedPath, scaleType.ordinal)
            .setLooping(false)
        if (dataSource.isValid()) {
            startDataSource(dataSource)
        }
    }

    private fun startDataSource(dataSource: DataSource) {
        controller?.start(dataSource)
    }

    fun attachView() {
        controller?.attachAlphaView(binding.playerContainer)
    }

    fun detachView() {
        controller?.detachAlphaView(binding.playerContainer)
    }

    fun releasePlayerController() {
        controller?.let {
            it.detachAlphaView(binding.playerContainer)
            it.release()
        }
        controller = null
        playerImpl = null
    }


    fun setScaleType(scaleType: ScaleType) {
        this.scaleType = scaleType
    }

    /**
     * 暂停播放
     */
    fun pauseVideo() {
        controller?.pause()
    }

    /**
     * 恢复播放
     */
    fun resumeVideo() {
        controller?.resume()
    }

    /**
     * 停止播放
     */
    fun stopVideo() {
        controller?.stop()
    }

    /**
     * 重置播放器
     */
    fun resetPlayer() {
        controller?.reset()
    }

    /**
     * 检查是否正在播放
     */
    fun isPlayerPlaying(): Boolean {
        return controller?.isPlaying() ?: false
    }

    /**
     * 设置循环播放
     */
    fun setLooping(looping: Boolean) {
        val dataSource = DataSource()
        dataSource.isLooping = looping
    }

    /**
     * 获取播放器实现
     */
    fun getPlayerImpl(): AbsPlayer? {
        return playerImpl
    }

    /**
     * 获取控制器
     */
    fun getController(): IPlayerController? {
        return controller
    }
}