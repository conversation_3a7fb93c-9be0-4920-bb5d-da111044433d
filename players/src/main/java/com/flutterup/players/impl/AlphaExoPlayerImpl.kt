package com.flutterup.players.impl

import android.content.Context
import android.util.Log
import android.view.Surface
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.VideoSize
import androidx.media3.exoplayer.ExoPlayer
import com.ss.ugc.android.alpha_player.model.VideoInfo
import com.ss.ugc.android.alpha_player.player.AbsPlayer

class AlphaExoPlayerImpl(private val context: Context) : AbsPlayer(context) {

    companion object {
        private const val PLAYER_TYPE = "ExoPlayer"
    }

    private lateinit var player: ExoPlayer

    private var videoSource: MediaItem? = null

    private var currVideoWidth: Int = 0

    private var currVideoHeight: Int = 0

    private val playerListener = object : Player.Listener {

        @Volatile
        private var isPlayWhenReady = false

        override fun onPlayerError(error: PlaybackException) {
            errorListener?.onError(0, 0, "ExoPlayer on error: " + Log.getStackTraceString(error))
        }

        override fun onPlayWhenReadyChanged(playWhenReady: <PERSON><PERSON><PERSON>, reason: Int) {
            isPlayWhenReady = playWhenReady
        }

        override fun onPlaybackStateChanged(playbackState: Int) {
            when (playbackState) {
                Player.STATE_READY -> {
                    if (isPlayWhenReady) {
                        preparedListener?.onPrepared()
                    }
                }
                Player.STATE_ENDED -> {
                    completionListener?.onCompletion()
                }
                else -> {}
            }
        }

        override fun onVideoSizeChanged(videoSize: VideoSize) {
            currVideoWidth = videoSize.width
            currVideoHeight = videoSize.height
        }

        override fun onRenderedFirstFrame() {
            firstFrameListener?.onFirstFrame()
        }
    }

    override fun getPlayerType(): String = PLAYER_TYPE


    override fun initMediaPlayer() {
        player = ExoPlayer.Builder(context).build()
        player.addListener(playerListener)
    }

    override fun setSurface(surface: Surface) {
        player.setVideoSurface(surface)
    }

    override fun setDataSource(dataPath: String) {
        videoSource = MediaItem.fromUri(dataPath)
        videoSource?.let {
            player.setMediaItem(it)
        }
    }

    override fun prepareAsync() {
        videoSource?.let {
            player.prepare()
            player.playWhenReady = true
        }
    }

    override fun start() {
        player.playWhenReady = true
        player.play()
    }

    override fun pause() {
        player.playWhenReady = false
        player.pause()
    }

    override fun stop() {
        player.stop()
    }

    override fun reset() {
        // 停止并清空播放队列
        player.stop()
        player.clearMediaItems()
        videoSource = null
        currVideoWidth = 0
        currVideoHeight = 0
    }

    override fun release() {
        player.removeListener(playerListener)
        player.release()
        videoSource = null
    }

    override fun setLooping(looping: Boolean) {
        player.repeatMode = if (looping) Player.REPEAT_MODE_ONE else Player.REPEAT_MODE_OFF
    }

    override fun setScreenOnWhilePlaying(onWhilePlaying: Boolean) {
    }

    override fun getVideoInfo(): VideoInfo {
        return VideoInfo(currVideoWidth, currVideoHeight)
    }

    /**
     * 检查播放器是否已初始化
     */
    fun isInitialized(): Boolean {
        return ::player.isInitialized
    }

    /**
     * 获取当前播放位置
     */
    fun getCurrentPosition(): Long {
        return if (isInitialized()) player.currentPosition else 0L
    }

    /**
     * 获取视频总时长
     */
    fun getDuration(): Long {
        return if (isInitialized()) player.duration else 0L
    }

    /**
     * 检查是否正在播放
     */
    fun isCurrentlyPlaying(): Boolean {
        return if (isInitialized()) player.isPlaying else false
    }

    /**
     * 设置新的视频源并准备播放
     * 支持多次调用以播放不同的视频
     */
    fun setVideoSourceAndPrepare(dataPath: String) {
        if (!isInitialized()) {
            initMediaPlayer()
        }

        // 重置播放器状态
        reset()

        // 设置新的数据源
        setDataSource(dataPath)

        // 准备播放
        prepareAsync()
    }
}