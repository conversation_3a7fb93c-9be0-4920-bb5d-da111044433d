package com.flutterup.base.utils

import java.text.SimpleDateFormat
import java.time.Instant
import java.time.LocalDate
import java.time.Period
import java.time.ZoneId
import java.time.format.DateTimeFormatter

object TextUtils {

    private const val MILLISECONDS = 1000
    private const val MINUTES = 60
    private const val HOURS = 60
    private const val DAYS = 24
    private const val YEARS = 365

    /**
     * 服务端常用的时间format
     */
    val defaultServerDateFormat = SimpleDateFormat("yyyy-MM-dd", LocaleUtils.currentLocale)
    val defaultServerDateTimeFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", LocaleUtils.currentLocale)

    fun formatBirthday(birthday: Long): String {
        return defaultServerDateFormat.format(birthday)
    }

    fun getAgeFromTimestamp(timestamp: Long): Int {
        val birthDate = Instant.ofEpochMilli(timestamp).atZone(ZoneId.systemDefault()).toLocalDate()
        val today = LocalDate.now()
        return Period.between(birthDate, today).years
    }

    fun getAgeFromDateString(dateString: String): Int {
        return try {
            val formatter = DateTimeFormatter.ofPattern(defaultServerDateFormat.toPattern())
            val birthDate = LocalDate.parse(dateString, formatter)
            val today = LocalDate.now()
            Period.between(birthDate, today).years
        } catch (_: Exception) {
            -1
        }
    }
}