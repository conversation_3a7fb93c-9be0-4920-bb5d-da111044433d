package com.flutterup.base

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext

private fun getBaseApplicationGlobalExceptionHandler(): CoroutineExceptionHandler? {
    return BaseApplication.getApplicationScope().coroutineContext[CoroutineExceptionHandler]
}

abstract class BaseRepositoryViewModel (
    private vararg val repositories: BaseRepository,
    exceptionHandler: CoroutineExceptionHandler? = null
) : BaseViewModel(exceptionHandler) {

    constructor(vararg repositories: BaseRepository) : this(
        repositories = repositories,
        exceptionHandler = getBaseApplicationGlobalExceptionHandler()
    )

    override fun onCleared() {
        super.onCleared()
        repositories.forEach { it.onClear() }
    }
}


/**
 * BaseViewModel
 *
 * @param exceptionHandler CoroutineExceptionHandler 协程异常处理器，可能为空
 */
abstract class BaseViewModel(
    exceptionHandler: CoroutineExceptionHandler? = null
) : ViewModel() {

    constructor() : this(getBaseApplicationGlobalExceptionHandler())

    private val _loadingState = MutableStateFlow(false)


    /**
     * loading 状态, 为什么是 protected 是想要你在 ViewModel中组合
     * ``` Kotlin
     *  val uiState: StateFlow<LoginEmailUIState> = combine(
     *         _uiState,
     *         loadingState
     *     ) { ui, loading ->
     *         ui.copy(isLoading = loading)
     *     }.stateIn(
     *         scope,
     *         SharingStarted.Eagerly,
     *         _uiState.value.copy(isLoading = loadingState.value)
     *     )
     * ```
     */
    protected val loadingState: StateFlow<Boolean> = _loadingState


    /**
     * 带异常处理的协程作用域，用于处理协程中的异常
     *
     * 如果构建器中传入了异常处理器，则使用传入的异常处理器
     * 否则使用 viewModelScope
     */
    protected val scope: CoroutineScope =
        if (exceptionHandler == null) {
            viewModelScope
        } else {
            viewModelScope.plus(exceptionHandler)
        }

    protected fun CoroutineScope.launchWithLoading(
        context: CoroutineContext = EmptyCoroutineContext,
        start: CoroutineStart = CoroutineStart.DEFAULT,
        block: suspend CoroutineScope.() -> Unit
    ) {
        launch(context, start) {
            _loadingState.update { true }
            try {
                block()
            } catch (e: Exception) {
                throw e
            } finally {
                _loadingState.update { false }
            }
        }
    }
}