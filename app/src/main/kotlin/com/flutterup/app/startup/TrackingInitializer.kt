package com.flutterup.app.startup

import android.content.Context
import android.os.Build
import androidx.startup.Initializer
import com.flutterup.app.BuildConfig
import com.flutterup.app.utils.UserRepository
import com.flutterup.base.BaseApplication
import com.flutterup.base.di.MMKVInitializer
import com.flutterup.base.store.MMKVStore
import com.flutterup.base.utils.DeviceUtils
import com.flutterup.base.utils.LocationUtils
import com.flutterup.base.utils.applicationEntryPoint
import com.flutterup.tracking.AdjustInitializer
import com.flutterup.tracking.TrackingManager
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.components.SingletonComponent

class TrackingInitializer : Initializer<TrackingManager> {


    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface TrackingEntryPoint {
        fun userRepository(): UserRepository
    }

    private val userRepository: UserRepository by lazy {
        applicationEntryPoint<TrackingEntryPoint>().userRepository()
    }

    override fun create(context: Context): TrackingManager {
        initProperties()
        return TrackingManager
    }

    private fun initProperties() {
        TrackingManager.setGlobalPresetProperties(mapOf(
            "os-version" to Build.VERSION.RELEASE.orEmpty(),
            "app_version" to BuildConfig.VERSION_NAME,
        ))

        TrackingManager.setGlobalDynamicPresetPropertiesProvider {
            val currentLocation = LocationUtils.getCachedLocation()

            val properties = mutableMapOf<String, Any>()
            properties["device_id"] = DeviceUtils.getDeviceId()
            properties["country"] = currentLocation?.country.orEmpty()
            properties["state"] = currentLocation?.state.orEmpty()
            properties["city"] = currentLocation?.city.orEmpty()

            properties["uid"] = userRepository.userId ?: 0
            properties["nickname"] = userRepository.nickname.orEmpty()
            properties["sex"] = userRepository.gender.toTrackingValue()
            properties["orientation"] = userRepository.orientationGender.toTrackingValue()
            properties["birthday"] = userRepository.birthdayFormatted
            properties["is_user"] = if (userRepository.isModel == true) 0 else 1
            properties["is_new"] = userRepository.isNewUser ?: 0

            properties["remain_pic"] = userRepository.right?.privacyImage ?: 0
            properties["remain_vid"] = userRepository.right?.privacyVideo ?: 0
            properties["remain_instantchat"] = userRepository.right?.flashChat ?: 0

            properties["location"] = "${currentLocation?.lat},${currentLocation?.lng}"
            properties["is_vip"] = if (userRepository.isVip) 1 else 0

            properties
        }
    }

    override fun dependencies(): List<Class<out Initializer<*>?>?> = listOf(AdjustInitializer::class.java, MMKVInitializer::class.java)
}