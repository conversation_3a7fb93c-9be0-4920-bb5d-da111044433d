package com.flutterup.app.utils

import com.flutterup.app.model.Gender
import com.flutterup.app.model.IUserInfo
import com.flutterup.app.model.MediaItem
import com.flutterup.app.model.UserInfo
import com.flutterup.base.store.MMKVStoreImpl
import com.flutterup.base.store.jsonValue
import com.flutterup.base.utils.JsonUtils
import com.flutterup.base.utils.TextUtils
import com.tencent.mmkv.MMKV
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UserRepository @Inject constructor(jsonUtils: JsonUtils) : IUserInfo {

    private val mmkvStore = MMKVStoreImpl(mmkv = MMKV.mmkvWithID("user"))

    private var _userInfo: UserInfo? by mmkvStore.jsonValue<UserInfo>("userInfo", jsonUtils = jsonUtils)

    /** 当前用户信息 */
    val userInfo: UserInfo? get() = _userInfo

    /** 是否登录 */
    val isLogin: Boolean get() = userId != null && token != null

    /**
     * 是否是无效用户, 是否需要重新引导用户走一遍信息设置
     * 1. 登陆了
     * 2. 没有昵称
     */
    val isInvalid: Boolean get() = isLogin && _userInfo?.isInvalid == true

    /** 是否是VIP */
    val isVip: Boolean get() = right?.vip == 1

    /** 性别 */
    val gender: Gender get() = Gender.Companion.fromValue(sex)

    /** 性取向 */
    val orientationGender: Gender get() = Gender.Companion.fromValue(sexuality)

    /** 生日字符串 */
    val birthdayFormatted: String get() = birthday?.let { TextUtils.formatBirthday(it) } ?: ""

    // 委托给userInfo的属性
    override val userId: Long? get() = _userInfo?.userId
    override val token: String? get() = _userInfo?.token
    override val imToken: String? get() = _userInfo?.imToken
    override val nickname: String? get() = _userInfo?.nickname
    override val headImage: String? get() = _userInfo?.headImage
    override val isNewUser: Int? get() = _userInfo?.isNewUser
    override val isModel: Boolean? get() = _userInfo?.isModel
    override val sign: String? get() = _userInfo?.sign
    override val sex: Int? get() = _userInfo?.sex
    override val sexuality: Int? get() = _userInfo?.sexuality
    override val age: Int? get() = _userInfo?.age
    override val isHide: Int? get() = _userInfo?.isHide
    override val birthday: Long? get() = _userInfo?.birthday
    override val mediaList: List<MediaItem>? get() = _userInfo?.mediaList
    override val tags: List<String>? get() = _userInfo?.tags
    override val online: Int? get() = _userInfo?.online
    override val right: IUserInfo.Right? get() = _userInfo?.right
    override val scene: Int? get() = _userInfo?.scene
    override val userFlag: Int? get() = _userInfo?.userFlag
    override val pushConfig: Int? get() = _userInfo?.pushConfig
    override val location: Int? get() = _userInfo?.location
    override val refer: Int? get() = _userInfo?.refer

    /**
     * 更新用户信息
     */
    fun updateUserInfo(userInfo: UserInfo?) {
        _userInfo = userInfo
    }

    /**
     * 登出
     */
    fun logout(isExpired: Boolean = false) {
        mmkvStore.clear()
    }
}