package com.flutterup.app.utils

import com.flutterup.app.navigation.GlobalNavCenter
import com.flutterup.app.network.environment.AppEnvManager.AppEnvEntryPoint
import com.flutterup.base.BaseApplication
import com.flutterup.base.store.MMKVStore
import com.flutterup.base.utils.Timber
import com.flutterup.base.utils.applicationEntryPoint
import com.flutterup.network.NetworkException
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineExceptionHandler
import retrofit2.HttpException
import java.io.IOException
import kotlin.coroutines.AbstractCoroutineContextElement
import kotlin.coroutines.CoroutineContext

/**
 * 全局协程异常处理器
 */
object GlobalCoroutineExceptionHandler : AbstractCoroutineContextElement(CoroutineExceptionHandler.Key),
    CoroutineExceptionHandler {

    private const val TAG = "GlobalCoroutineExceptionHandler"

    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface CoroutineExceptionHandlerEntryPoint {
        fun userRepository(): UserRepository

        fun navCenter(): GlobalNavCenter
    }

    private val userRepository: UserRepository by lazy {
        applicationEntryPoint<CoroutineExceptionHandlerEntryPoint>().userRepository()
    }

    private val navCenter: GlobalNavCenter by lazy {
        applicationEntryPoint<CoroutineExceptionHandlerEntryPoint>().navCenter()
    }

    override fun handleException(context: CoroutineContext, exception: Throwable) {
        when (exception) {
            is NetworkException -> {
                // 处理自定义的网络异常
                Timber.e(TAG, "Caught NetworkException: " +
                        "url=${exception.url}, code=${exception.code}, message=${exception.message}, data=${exception.data}")
                // - 根据 exception.code 显示不同的用户提示 (Toast, Snackbar)
                // - 上报异常到统计平台
                // - 尝试重新登录（如果是认证失败相关的 code）
                if (exception.code == 1002) {
                    Timber.showToast("Authentication failed, please log in again.")
                    userRepository.logout(isExpired = true)
                    navCenter.navigateLogin()
                } else {
                    if (!exception.message.isNullOrEmpty()) {
                        Timber.showToast(exception.message)
                    }
                }
            }
            is HttpException -> {
                // 处理 Retrofit 的 HTTP 异常
                val code = exception.code()
                val errorBody = exception.response()?.errorBody()?.string()
                Timber.e(TAG, "Caught HttpException: code=$code, errorBody=$errorBody")
                // - 显示通用网络错误提示
                // - 解析 errorBody 获取更详细的错误信息
                Timber.showToast("Network Error")
            }
            is IOException -> {
                // 处理其他 IO 异常 (例如网络连接超时、无法连接主机等)
                Timber.e(TAG, "Caught IOException: ${exception.message}")
                // - 显示网络连接错误提示
                Timber.showToast("Network Error")
            }

            //TODO 处理im异常
//            is ChatCoreException -> {
//                //处理im异常
//                Timber.e(TAG, "Caught ChatCoreException: code=${exception.code},",
//                    "coreCode=${exception.coreCode}, message=${exception.message}", exception)
//                if (exception.message != null) {
//                    showToast(exception.message)
//                }
//            }
            else -> {
                // 处理其他未捕获的异常
                Timber.e(TAG, "Caught unexpected exception: ${exception.message}")
                // - 显示通用错误提示
                // - 上报未知异常
            }
        }
    }
}