package com.flutterup.app.navigation

import androidx.annotation.MainThread
import androidx.navigation.NavHostController
import androidx.navigation.NavOptions
import androidx.navigation.NavOptionsBuilder
import androidx.navigation.Navigator
import com.flutterup.app.utils.UserRepository
import com.flutterup.app.screen.HomeBaseRoute
import com.flutterup.app.screen.login.LoginBaseRoute
import com.flutterup.app.screen.login.LoginProfileCompleteRoute
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 全局导航中心
 * 用于管理全局导航相关逻辑
 */
@Singleton
class GlobalNavCenter @Inject constructor(
    private val userRepository: UserRepository
) {
    private var navController: NavHostController? = null

    fun setNavController(navController: NavHostController) {
        this.navController = navController
    }

    fun navigateLogin() {
        navController?.navigate(LoginBaseRoute)
    }

    /**
     * 导航到首页, 会检查是是无效用户
     */
    fun navigateHome() {
        if (userRepository.isInvalid) { //是无效用户, 跳转资料填写页
            navController?.navigate(LoginProfileCompleteRoute)
            return
        }

        navController?.navigate(HomeBaseRoute)
    }
}