package com.flutterup.app.screen.login.state

import com.flutterup.app.model.Gender
import com.flutterup.base.BaseState

data class LoginProfileUIState(
    val nickname: String = "",
    val age: Int = AGE_UNSPECIFIED,
    val ageLimit: Int = 2,
    val gender: Gender = Gender.MALE,
    val meet: Gender = Gender.FEMALE,
    override val isLoading: Boolean = false,
) : BaseState {

    val ageText: String
        get() = if (age <= 0) "" else age.toString()

    /**
     * 数字进行校验，18~70
     */
    val isAgeValid: Boolean get() {
        if (age == AGE_UNSPECIFIED) return true
        return age in SUPPORTED_AGES
    }

    /**
     * 是否可以继续
     */
    val isContinueEnabled: Boolean
        get() = nickname.isNotEmpty()
                && age in SUPPORTED_AGES
                && gender != Gender.UNSPECIFIED
                && meet != Gender.UNSPECIFIED

    companion object {
        const val AGE_UNSPECIFIED = -1

        private val SUPPORTED_AGES = IntRange(18, 70)
    }
}