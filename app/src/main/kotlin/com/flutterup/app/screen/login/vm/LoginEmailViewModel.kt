package com.flutterup.app.screen.login.vm

import android.util.Patterns
import androidx.lifecycle.ViewModel
import com.flutterup.app.screen.login.state.LoginEmailUIState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class LoginEmailViewModel @Inject constructor() : ViewModel() {
    private val _uiState = MutableStateFlow(LoginEmailUIState())
    val uiState: StateFlow<LoginEmailUIState>
        get() = _uiState.asStateFlow()

    fun updateEmail(email: String) = _uiState.update {
        it.copy(email = email, isEmailValid = isEmailValid(email))
    }

    fun updatePassword(password: String) = _uiState.update {
        it.copy(password = password, isPasswordValid = isPasswordValid(password))
    }

    fun togglePasswordVisibility() = _uiState.update {
        it.copy(isPasswordVisible = !it.isPasswordVisible)
    }

    /**
     * 检查邮箱格式是否正确
     */
    private fun isEmailValid(email: String): Boolean {
        if (email.isEmpty()) return true

        return Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }

    /**
     * 检查密码格式是否正确
     */
    private fun isPasswordValid(password: String): Boolean {
        if (password.isEmpty()) return true

        return password.length >= 8 && password.any { it.isLetter() } && password.any { it.isDigit() }
    }
}