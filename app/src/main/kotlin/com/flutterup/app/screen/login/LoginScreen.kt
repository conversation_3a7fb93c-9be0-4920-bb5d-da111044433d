package com.flutterup.app.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.flutterup.app.R
import com.flutterup.app.design.component.AppRadioButton
import com.flutterup.app.design.noRippleSelectable
import com.flutterup.app.design.text.BodySize
import com.flutterup.app.design.text.BodyText
import com.flutterup.app.design.text.LabelText
import com.flutterup.app.design.text.buildAppAnnotatedString
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.PurpleCoverColor
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.webview.WebViewRoute
import com.flutterup.app.design.component.ktx.toHazeStyle
import com.flutterup.app.design.component.ktx.toHazeTint
import com.flutterup.app.screen.login.content.GoogleSignInOutlinedButton
import com.flutterup.app.screen.login.vm.LoginHomeViewModel
import dev.chrisbanes.haze.HazeState
import dev.chrisbanes.haze.hazeEffect
import dev.chrisbanes.haze.hazeSource
import dev.chrisbanes.haze.rememberHazeState

@Composable
fun LoginScreen(
    modifier: Modifier = Modifier,
    navController: NavController = LocalNavController.current,
    hazeState: HazeState = rememberHazeState(),
    viewModel: LoginHomeViewModel = hiltViewModel(),
) {
    Surface(
        modifier = modifier.fillMaxSize()
    ) {
        Box(
            modifier = Modifier.fillMaxWidth()
        ) {
            LoginBackground(hazeState)

            Box(
                modifier = Modifier
                    .wrapContentSize()
                    .align(Alignment.Center)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                ) {
                    LoginLogo(
                        modifier = Modifier
                            .wrapContentSize()
                            .align(Alignment.CenterHorizontally)
                    )

                    Spacer(modifier = Modifier.height(30.dp))

                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .wrapContentHeight()
                            .padding(horizontal = 24.dp)
                    ) {
                        GoogleSignInOutlinedButton(
                            onClick = {
                                viewModel.checkAgreementPolicy { viewModel.showGoogleSignIn() }
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(50.dp)
                                .clip(ButtonDefaults.outlinedShape)
                                .hazeEffect(
                                    state = hazeState,
                                    style = PurpleCoverColor.toHazeTint().toHazeStyle(),
                                    block = { blurEnabled = true }
                                )
                        )

                        Spacer(modifier = Modifier.height(20.dp))

                        OutlinedButton(
                            onClick = {
                                viewModel.checkAgreementPolicy { navController.navigate(LoginNormalRoute) }
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(50.dp)
                                .clip(ButtonDefaults.outlinedShape)
                                .hazeEffect(
                                    state = hazeState,
                                    style = PurpleCoverColor.toHazeTint().toHazeStyle(),
                                    block = { blurEnabled = true }
                                )
                        ) {
                            BodyText(
                                text = stringResource(R.string.continue_with_email),
                                size = BodySize.Large
                            )
                        }
                    }
                }
            }

            Box(
                modifier = Modifier
                    .wrapContentHeight()
                    .fillMaxWidth()
                    .padding(
                        horizontal = 24.dp,
                        vertical = 21.dp
                    )
                    .navigationBarsPadding()
                    .align(Alignment.BottomCenter)
            ) {
                Row(
                    modifier = Modifier
                        .noRippleSelectable(
                            selected = viewModel.isAgreementPolicy,
                            onClick = { viewModel.rippleAgreementPolicy() },
                            role = Role.RadioButton
                        )
                ) {
                    AppRadioButton(
                        selected = viewModel.isAgreementPolicy,
                        onClick = null,
                        modifier = Modifier.size(12.dp),
                    )

                    Spacer(modifier = Modifier.width(5.dp))

                    AgreementText(
                        navController = navController,
                        onClick = { viewModel.rippleAgreementPolicy() }
                    )
                }
            }
        }
    }

    // Google 登录弹窗
    if (viewModel.showGoogleSignInDialog) {
        GoogleSignInDialog(
            clientId = "lalala.apps.googleusercontent.com",
            onResult = { result ->
                viewModel.handleGoogleSignInResult(result)
            },
            onDismiss = {
                viewModel.hideGoogleSignIn()
            }
        )
    }
}

@Composable
private fun AgreementText(
    navController: NavController,
    onClick: () -> Unit = {}
) {
    val prefix = stringResource(R.string.login_agreement_prefix)
    val termsPolicy = stringResource(R.string.terms_policy)
    val privacyPolicy = stringResource(R.string.privacy_policy)
    val delimiter = stringResource(R.string.login_agreement_delimiter)

    val annotatedString = buildAppAnnotatedString {
        appendUrl(
            text = prefix,
            url = "",
            clickable = { onClick() }
        )

        appendLinkUnderline(
            text = termsPolicy,
            tag = termsPolicy,
            clickable = {
                navController.navigate(WebViewRoute(
                    title = termsPolicy,
                    url = "https://www.localuvapp.com/#/terms"
                ))
            }
        )

        append(delimiter)

        appendLinkUnderline(
            text = privacyPolicy,
            tag = privacyPolicy,
            clickable = {
                navController.navigate(WebViewRoute(
                    title = privacyPolicy,
                    url = "https://www.localuvapp.com/#/pp"
                ))
            }
        )
    }

    LabelText(text = annotatedString)
}

@Composable
private fun LoginLogo(modifier: Modifier) {
    Column(modifier = modifier) {
        Image(
            painter = painterResource(R.drawable.ic_launcher_logo),
            contentDescription = null
        )

        Spacer(modifier = Modifier.height(8.dp))

        Image(
            painter = painterResource(R.drawable.ic_launcher_text),
            contentDescription = null
        )
    }
}

@Composable
private fun LoginBackground(state: HazeState) {
    Box(modifier = Modifier.fillMaxSize()) {
        Image(
            painter = painterResource(id = R.mipmap.ic_login_background),
            contentDescription = null,
            modifier = Modifier
                .fillMaxSize()
                .hazeSource(state, zIndex = 0f),
            contentScale = ContentScale.Crop
        )

        Spacer(
            modifier = Modifier
                .fillMaxSize()
                .background(PurpleCoverColor)
                .hazeSource(state, zIndex = 1f)
        )
    }
}


@Preview
@Composable
private fun LoginScreenPreview() {
    AppTheme {
        LoginScreen(navController = rememberNavController())
    }
}