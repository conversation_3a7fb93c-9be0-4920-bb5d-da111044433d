package com.flutterup.app.screen.login.content

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import com.flutterup.app.design.noRippleClickable
import com.flutterup.app.design.text.BodySize
import com.flutterup.app.design.text.BodyText
import com.flutterup.app.design.theme.PurpleTextFieldColors

@Composable
fun LoginEmailPwdTextField(
    value: String,
    keyboardType: KeyboardType,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    isError: Boolean = false,
    isSecurity: Boolean = false,
    @StringRes placeholder: Int? = null,
    @StringRes supportingText: Int? = null,
    @DrawableRes leadingIcon: Int? = null,
    @DrawableRes trailingIcon: Int? = null,
    onLeadingIconClick: () -> Unit = {},
    onTrailingIconClick: () -> Unit = {},
) {
    val passwordVisualTransformation = remember { PasswordVisualTransformation() }

    TextField(
        value = value,
        onValueChange = onValueChange,
        modifier = modifier,
        isError = isError,
        placeholder = {
            if (placeholder != null) {
                BodyText(
                    text = stringResource(placeholder),
                    size = BodySize.Small,
                )
            }
        },
        supportingText = {
            if (isError && supportingText != null) {
                BodyText(
                    text = stringResource(supportingText),
                    size = BodySize.Small,
                )
            }
        },
        leadingIcon = {
            if (leadingIcon != null) {
                Icon(
                    imageVector = ImageVector.vectorResource(id = leadingIcon),
                    contentDescription = null,
                    modifier = Modifier.size(20.dp).noRippleClickable(onClick = onLeadingIconClick)
                )
            }
        },
        trailingIcon = {
            if (trailingIcon != null) {
                Image(
                    painter = painterResource(id = trailingIcon),
                    contentDescription = null,
                    modifier = Modifier.size(20.dp).noRippleClickable(onClick = onTrailingIconClick)
                )
            }
        },
        visualTransformation = if (isSecurity) passwordVisualTransformation else VisualTransformation.None,
        keyboardOptions = KeyboardOptions(
            keyboardType = keyboardType,
            imeAction = if (keyboardType == KeyboardType.Password) ImeAction.Done else ImeAction.Next
        ),
        colors = PurpleTextFieldColors,
        singleLine = true,
        shape = RoundedCornerShape(8.dp),
    )
}