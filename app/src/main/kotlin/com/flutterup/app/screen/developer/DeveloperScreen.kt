@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.developer

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.gift.GiftViewModel


@Composable
internal fun DeveloperScreen(
    navController: NavController = LocalNavController.current,
    giftViewModel: GiftViewModel = hiltViewModel()
) {
    giftViewModel.preloadGifts()

    AppScaffold(
        title = { Text(text = "Developer") },
        onBackClick = { navController.popBackStack() },
        modifier = Modifier.fillMaxSize()
    ) {
        LazyColumn(
            modifier = Modifier.fillMaxSize().padding(it)
        ) {
            developerItem("Gift") {
                navController.navigate(DeveloperGiftRoute)
            }
        }
    }
}

private fun LazyListScope.developerItem(
    title: String,
    onClick: () -> Unit
) {
    item {
        TextButton(
            onClick = onClick,
            modifier = Modifier.fillMaxSize(),
        ) {
            Text(
                text = title,
                color = Color.Black,
                fontSize = 20.sp,
            )
        }
    }
}

@Preview
@Composable
private fun DeveloperScreenPreview() {
    AppTheme {
        DeveloperScreen(
            navController = rememberNavController()
        )
    }
}
