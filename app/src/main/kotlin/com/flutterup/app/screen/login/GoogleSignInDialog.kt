@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.login

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.flutterup.app.screen.login.helper.GoogleOAuthHelper
import com.flutterup.app.screen.login.helper.GoogleSignInResult
import com.flutterup.app.screen.webview.CustomWebView

/**
 * Google 登录弹窗组件
 */
@Composable
fun GoogleSignInDialog(
    clientId: String,
    onResult: (GoogleSignInResult) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    val authUrl = remember { GoogleOAuthHelper.buildAuthUrl(clientId) }
    val isLoading = remember { mutableStateOf(true) }
    
    Dialog(
        onDismissRequest = {
            onResult(GoogleSignInResult.Cancelled)
            onDismiss()
        },
        properties = DialogProperties(
            usePlatformDefaultWidth = false,
            dismissOnBackPress = true,
            dismissOnClickOutside = false
        )
    ) {
        Card(
            modifier = modifier
                .wrapContentSize()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Box(
                modifier = Modifier.fillMaxSize()
            ) {
                CustomWebView(
                    url = authUrl,
                    modifier = Modifier.fillMaxSize(),
                    onBackPressed = {
                        onResult(GoogleSignInResult.Cancelled)
                        onDismiss()
                    },
                    onUrlChange = { url ->
                        isLoading.value = false

                        // 检查是否为回调 URL
                        if (GoogleOAuthHelper.isCallbackUrl(url)) {
                            val authCode = GoogleOAuthHelper.extractAuthorizationCode(url)
                            val error = GoogleOAuthHelper.extractError(url)

                            when {
                                authCode != null -> {
                                    onResult(GoogleSignInResult.Success(authCode))
                                    onDismiss()
                                }

                                error != null -> {
                                    onResult(GoogleSignInResult.Error(error))
                                    onDismiss()
                                }

                                else -> {
                                    onResult(GoogleSignInResult.Error("Unknown error"))
                                    onDismiss()
                                }
                            }
                        }
                    }
                )

                if (isLoading.value) {
                    CircularProgressIndicator(
                        color = Color.Black,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
            }
        }
    }
}
