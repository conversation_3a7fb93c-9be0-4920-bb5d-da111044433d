@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.screen.LocalNavController


@Composable
fun LoginNormalScreen() {
    val navController = LocalNavController.current

    LoginNormalScreen(
        navController = navController
    )
}


@Composable
private fun LoginNormalScreen(
    navController: NavHostController
) {
    AppScaffold(
        title = { },
        onBackClick = { navController.popBackStack() },
        modifier = Modifier.fillMaxSize(),
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
            containerColor = Color.Transparent,
        )
    ) {
        LoginNormalContent(it)
    }
}

@Composable
private fun LoginNormalContent(paddingValues: PaddingValues) {
    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        LoginNormalTopContent(
            modifier = Modifier.align(Alignment.TopCenter)
        )
    }
}

@Composable
private fun LoginNormalTopContent(
    modifier: Modifier = Modifier
) {

    Box(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentSize()
    ) {
        Image(
            painter = painterResource(R.mipmap.ic_login_background),
            contentDescription = null,
            contentScale = ContentScale.None,
        )
    }
}

@Preview
@Composable
private fun LoginNormalScreenPreview() {
    AppTheme {
        LoginNormalScreen(
            navController = rememberNavController()
        )
    }
}