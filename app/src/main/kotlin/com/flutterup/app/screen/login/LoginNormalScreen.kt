@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.TextField
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.text.AppFontFamily
import com.flutterup.app.design.text.BodySize
import com.flutterup.app.design.text.BodyText
import com.flutterup.app.design.text.HeadlineSize
import com.flutterup.app.design.text.HeadlineText
import com.flutterup.app.design.text.TitleSize
import com.flutterup.app.design.text.TitleText
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.PurpleCoverColor
import com.flutterup.app.design.theme.PurplePrimary
import com.flutterup.app.design.theme.PurpleTextFieldColors
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.login.state.LoginEmailUIState
import com.flutterup.app.screen.login.vm.LoginEmailViewModel


@Composable
fun LoginNormalScreen() {
    val navController = LocalNavController.current

    LoginNormalScreen(
        navController = navController
    )
}


@Composable
private fun LoginNormalScreen(
    navController: NavHostController,
    viewModel: LoginEmailViewModel = hiltViewModel()
) {
    AppScaffold(
        title = { },
        onBackClick = { navController.popBackStack() },
        modifier = Modifier.fillMaxSize(),
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
            containerColor = Color.Transparent,
        )
    ) {
        LoginNormalContent(it, viewModel)
    }
}

@Composable
private fun LoginNormalContent(
    paddingValues: PaddingValues,
    viewModel: LoginEmailViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        LoginNormalTopContent(
            modifier = Modifier.align(Alignment.TopCenter)
        )

        LoginWelcome(
            modifier = Modifier.align(Alignment.TopCenter)
                .padding(top = paddingValues.calculateTopPadding())
                .padding(top = 33.dp)
        )

        Column(
            modifier = Modifier.fillMaxSize()
                .padding(top = paddingValues.calculateTopPadding())
                .padding(top = 137.dp)
                .clip(shape = RoundedCornerShape(
                    topStart = 32.dp,
                    topEnd = 32.dp,
                    bottomStart = 0.dp,
                    bottomEnd = 0.dp
                ))
                .background(Color.White)
                .align(Alignment.BottomCenter)
        ) {
            Spacer(Modifier.height(32.dp))

            LoginEmail(
                uiState = uiState,
                viewModel = viewModel,
            )

            Spacer(Modifier.height(20.dp))

            LoginPassword(
                uiState = uiState,
                viewModel = viewModel,
            )
        }
    }
}

@Composable
private fun LoginNormalTopContent(
    modifier: Modifier = Modifier
) {

    Box(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight()
    ) {
        Image(
            painter = painterResource(R.mipmap.ic_login_top_background),
            contentDescription = null,
            contentScale = ContentScale.FillWidth,
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(
            modifier = Modifier.matchParentSize()
                .background(PurpleCoverColor)
        )
    }
}

@Composable
private fun LoginWelcome(
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Row {
            Image(
                painter = painterResource(R.drawable.ic_launcher_logo),
                contentDescription = null,
                modifier = Modifier.size(28.dp)
            )

            Spacer(modifier = Modifier.width(1.dp))

            TitleText(
                text = stringResource(R.string.app_name),
                size = TitleSize.Medium,
                fontType = AppFontFamily.GrayDesignRegular,
                modifier = Modifier.align(Alignment.CenterVertically)
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        HeadlineText(
            text = stringResource(R.string.login_welcome),
            size = HeadlineSize.Large,
            bold = true,
        )
    }
}

@Composable
private fun LoginEmail(
    uiState: LoginEmailUIState,
    viewModel: LoginEmailViewModel,
) {
    TextField(
        value = uiState.email,
        onValueChange = { viewModel.updateEmail(it) },
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        isError = !uiState.isEmailValid,
        placeholder = {
            BodyText(
                stringResource(R.string.email),
                size = BodySize.Medium,
                color = PurplePrimary
            )
        },
        leadingIcon = {
            Icon(
                imageVector = ImageVector.vectorResource(id = R.drawable.ic_email),
                contentDescription = null,
                modifier = Modifier.size(20.dp)
            )
        },
        supportingText = {
            if (!uiState.isEmailValid) {
                BodyText(
                    text = stringResource(R.string.login_email_format_error),
                    size = BodySize.Small,
                    color = PurplePrimary
                )
            }
        },
        colors = PurpleTextFieldColors,
        shape = RoundedCornerShape(8.dp),
    )
}

@Composable
private fun LoginPassword(
    uiState: LoginEmailUIState,
    viewModel: LoginEmailViewModel,
) {
    TextField(
        value = uiState.password,
        onValueChange = { viewModel.updatePassword(it) },
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        isError = !uiState.isPasswordValid,
        placeholder = {
            BodyText(
                stringResource(R.string.password),
                size = BodySize.Medium,
                color = PurplePrimary
            )
        },
        leadingIcon = {
            Icon(
                imageVector = ImageVector.vectorResource(id = R.drawable.ic_password),
                contentDescription = null,
                modifier = Modifier.size(20.dp)
            )
        },
        trailingIcon = {
            Image(
                painter = painterResource(R.drawable.ic_password_visibility),
                contentDescription = null,
                modifier = Modifier.size(18.dp)
                    .clickable { viewModel.togglePasswordVisibility() }
            )
        },
        supportingText = {
            if (!uiState.isPasswordValid) {
                BodyText(
                    text = stringResource(R.string.login_password_format_error),
                    size = BodySize.Small,
                    color = PurplePrimary
                )
            }
        },
        visualTransformation = if (uiState.isPasswordVisible) VisualTransformation.None else PasswordVisualTransformation(),
        colors = PurpleTextFieldColors,
        shape = RoundedCornerShape(8.dp),
    )
}

@Preview
@Composable
private fun LoginNormalScreenPreview() {
    AppTheme {
        LoginNormalScreen(
            navController = rememberNavController()
        )
    }
}