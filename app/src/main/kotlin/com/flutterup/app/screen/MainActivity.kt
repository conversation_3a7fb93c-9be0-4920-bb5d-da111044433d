package com.flutterup.app.screen

import android.os.Bundle
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import com.flutterup.app.design.component.AppBackground
import com.flutterup.app.navigation.AppScreen
import com.flutterup.base.compose.BaseComposeActivity
import com.flutterup.network.impl.NetworkMonitor
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.navigation.GlobalNavCenter
import com.flutterup.app.screen.gift.GiftViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : BaseComposeActivity() {

    @Inject
    lateinit var networkMonitor: NetworkMonitor

    @Inject
    lateinit var navCenter: GlobalNavCenter

    private val viewModel: MainActivityViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        val splashScreen = installSplashScreen()
        super.onCreate(savedInstanceState)

        // 控制启动页显示时机
        splashScreen.setKeepOnScreenCondition {
            val uiState = viewModel.uiState.value
            uiState.shouldKeepSplashScreen()
        }
    }

    @Composable
    override fun Screen() {
        val appState = rememberAppState(
            networkMonitor = networkMonitor,
            navCenter = navCenter
        )
        val uiState by viewModel.uiState.collectAsState()

        CompositionLocalProvider(
            LocalAppState provides appState,
            LocalNavController provides appState.navController,
        ) {
            AppTheme {
                AppBackground {
                    AppScreen(uiState)
                }
            }
        }
    }
}