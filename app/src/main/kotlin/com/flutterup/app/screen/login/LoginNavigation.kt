package com.flutterup.app.screen.login

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import com.flutterup.app.screen.MainActivityUIState
import kotlinx.serialization.Serializable

@Serializable
data object LoginRoute

@Serializable
data object LoginBaseRoute

@Serializable
data object LoginNormalRoute

@Serializable
data object LoginProfileCompleteRoute


fun NavGraphBuilder.loginGraph(state: MainActivityUIState) {
    val startDestination = when (state) {
        is MainActivityUIState.UncompletedProfile -> LoginProfileCompleteRoute::class
        else -> LoginRoute::class
    }

    navigation<LoginBaseRoute>(startDestination) {
        composable(route = LoginRoute::class) {
            LoginScreen()
        }

        composable(route = LoginNormalRoute::class) {
            LoginNormalScreen()
        }

        composable(route = LoginProfileCompleteRoute::class) {
            LoginProfileCompleteScreen()
        }
    }
}