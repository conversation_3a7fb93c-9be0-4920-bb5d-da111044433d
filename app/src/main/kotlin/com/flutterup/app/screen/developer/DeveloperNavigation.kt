package com.flutterup.app.screen.developer

import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import com.flutterup.app.BuildConfig
import com.flutterup.app.screen.gift.GiftUIState
import com.flutterup.app.screen.gift.GiftViewModel
import kotlinx.serialization.Serializable


@Serializable data object DeveloperRoute

@Serializable data object DeveloperBaseRoute

@Serializable data object DeveloperGiftRoute

fun NavGraphBuilder.developerGraph() {
    navigation<DeveloperBaseRoute>(startDestination = DeveloperRoute::class) {
        composable(route = DeveloperRoute::class) {
            DeveloperScreen()
        }

        composable(route = DeveloperGiftRoute::class) {
            DeveloperGiftScreen()
        }
    }
}

fun NavController.navigateToDeveloper() {
    if (!BuildConfig.DEBUG) return //仅在Debug模式下可用
    navigate(DeveloperBaseRoute)
}