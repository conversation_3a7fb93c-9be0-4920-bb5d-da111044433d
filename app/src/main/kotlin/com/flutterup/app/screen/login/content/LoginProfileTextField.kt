package com.flutterup.app.screen.login.content

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.selection.LocalTextSelectionColors
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.TextFieldColors
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.flutterup.app.R
import com.flutterup.app.design.text.BodySize
import com.flutterup.app.design.text.BodyText
import com.flutterup.app.design.text.LabelSize
import com.flutterup.app.design.text.LabelText
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.IndicatorTransparentTextFieldColors


@ExperimentalMaterial3Api
@Composable
fun LoginProfileTextField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    keyboardType: KeyboardType = KeyboardType.Text,
    @DrawableRes labelLeadingIcon: Int? = null,
    @DrawableRes labelTrailingIcon: Int? = null,
    @StringRes label: Int? = null,
    @StringRes placeholder: Int? = null,
    @StringRes supporting: Int? = null,
    isError: Boolean = false,
    limitNum: Int = Int.MAX_VALUE,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    colors: TextFieldColors = IndicatorTransparentTextFieldColors,
) {
    Column(modifier = modifier) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
        ) {
            Row(
                modifier = Modifier
                    .wrapContentSize()
                    .align(Alignment.CenterStart)
            ) {
                if (labelLeadingIcon != null) {
                    Icon(
                        painter = painterResource(id = labelLeadingIcon),
                        contentDescription = null,
                        tint = colors.focusedLabelColor,
                        modifier = Modifier
                            .size(16.dp)
                            .align(Alignment.CenterVertically)
                    )

                    Spacer(modifier = Modifier.width(5.dp))
                }

                if (label != null) {
                    LabelText(
                        text = stringResource(label),
                        size = LabelSize.Large,
                        modifier = Modifier.align(Alignment.CenterVertically),
                        color = colors.focusedLabelColor,
                    )
                }
            }

            if (labelTrailingIcon != null) {
                Icon(
                    painter = painterResource(id = labelTrailingIcon),
                    tint = colors.focusedLabelColor,
                    contentDescription = null,
                    modifier = Modifier
                        .size(24.dp)
                        .align(Alignment.CenterEnd)
                )
            }
        }

        CompositionLocalProvider(LocalTextSelectionColors provides colors.textSelectionColors) {
            Column {
                BasicTextField(
                    value = value,
                    onValueChange = { if (it.length <= limitNum) onValueChange(it) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight(),
                    singleLine = true,
                    textStyle = MaterialTheme.typography.bodyLarge,
                    enabled = enabled,
                    keyboardOptions = KeyboardOptions(
                        keyboardType = keyboardType,
                        imeAction = ImeAction.Next
                    ),
                ) { innerTextField ->
                    TextFieldDefaults.DecorationBox(
                        value = value,
                        visualTransformation = visualTransformation,
                        innerTextField = innerTextField,
                        placeholder = {
                            if (placeholder != null) {
                                BodyText(
                                    text = stringResource(placeholder),
                                    size = BodySize.Small,
                                )
                            }
                        },
                        singleLine = true,
                        enabled = enabled,
                        isError = isError,
                        interactionSource = interactionSource,
                        colors = colors,
                        contentPadding = PaddingValues(bottom = 8.dp),
                    )
                }

                Box(modifier = Modifier.fillMaxWidth()) {
                    if (isError && supporting != null) {
                        BodyText(
                            text = stringResource(supporting),
                            size = BodySize.Small,
                            color = colors.errorSupportingTextColor,
                            modifier = Modifier.align(Alignment.CenterStart)
                        )
                    }

                    if (limitNum != Int.MAX_VALUE) {
                        BodyText(
                            text = "${value.length}/$limitNum",
                            size = BodySize.Small,
                            color = colors.focusedSupportingTextColor,
                            modifier = Modifier.align(Alignment.CenterEnd)
                        )
                    }
                }
            }
        }
    }
}


@ExperimentalMaterial3Api
@Preview(backgroundColor = 0xFFFFFFFF, showBackground = true)
@Composable
private fun LoginProfileTextFieldPreview() {
    AppTheme {
        LoginProfileTextField(
            value = "lalala",
            onValueChange = {},
            placeholder = R.string.nickname,
            labelLeadingIcon = R.drawable.ic_profile_age,
            labelTrailingIcon = R.drawable.ic_arrow_down,
            label = R.string.nickname,
            limitNum = 3,
        )
    }
}