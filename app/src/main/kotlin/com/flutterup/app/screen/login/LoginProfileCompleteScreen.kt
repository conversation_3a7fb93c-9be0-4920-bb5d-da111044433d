@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppContinueButton
import com.flutterup.app.design.noRippleClickable
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.model.Gender
import com.flutterup.app.screen.GenderDialog
import com.flutterup.app.screen.LocalAppState
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.login.content.LoginProfileTextField
import com.flutterup.app.screen.login.state.LoginProfileUIState
import com.flutterup.app.screen.login.vm.LoginProfileViewModel


/**
 * 资料填写完成页
 */
@Composable
fun LoginProfileCompleteScreen() {
    val appState = LocalAppState.current
    val navController = LocalNavController.current
    val viewModel: LoginProfileViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LoginProfileCompleteContent(
        uiState = uiState,
        canGoBack = appState.canGoBack(),
        onBackClick = { navController.popBackStack() },
        onNicknameChange = { viewModel.updateNickname(it) },
        onGenderChange = { viewModel.updateGender(it) },
        onMeetChange = { viewModel.updateMeet(it) },
        onAgeChange = { viewModel.updateAge(it.toIntOrNull() ?: LoginProfileUIState.AGE_UNSPECIFIED) },
        onContinueClick = { viewModel.complete() },
    )
}

@Composable
private fun LoginProfileCompleteContent(
    uiState: LoginProfileUIState,
    canGoBack: Boolean = true,
    onBackClick: () -> Unit = {},
    onNicknameChange: (String) -> Unit = {},
    onGenderChange: (Gender) -> Unit = {},
    onMeetChange: (Gender) -> Unit = {},
    onAgeChange: (String) -> Unit = {},
    onContinueClick: () -> Unit = {},
) {
    var isShownGenderDialog by remember { mutableStateOf(false) }
    var isShownMeetDialog by remember { mutableStateOf(false) }
    var isShownAgeDialog by remember { mutableStateOf(false) }

    AppScaffold(
        title = { },
        canGoBack = canGoBack,
        onBackClick = onBackClick,
        modifier = Modifier.fillMaxSize(),
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) {
        Column (modifier = Modifier.fillMaxSize()) {
            LoginProfileCompleteTopContent()

            Spacer(modifier = Modifier.height(24.dp))

            LoginProfileTextField(
                value = uiState.nickname,
                onValueChange = onNicknameChange,
                placeholder = R.string.nickname,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 20.dp),
            )

            Spacer(modifier = Modifier.height(16.dp))

            LoginProfileTextField(
                value = stringResource(uiState.gender.stringResource),
                onValueChange = {},
                labelLeadingIcon = R.drawable.ic_profile_gender,
                labelTrailingIcon = R.drawable.ic_arrow_down,
                label = R.string.gender_label,
                placeholder = R.string.gender_label,
                enabled = false,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 20.dp)
                    .noRippleClickable(onClick = { isShownGenderDialog = true }),
            )

            Spacer(modifier = Modifier.height(16.dp))

            LoginProfileTextField(
                value = stringResource(uiState.meet.stringResource),
                onValueChange = {},
                labelLeadingIcon = R.drawable.ic_profile_want_meet,
                labelTrailingIcon = R.drawable.ic_arrow_down,
                label = R.string.meet_label,
                placeholder = R.string.meet_label,
                enabled = false,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 20.dp)
                    .noRippleClickable(onClick = { isShownMeetDialog = true }),
            )

            Spacer(modifier = Modifier.height(16.dp))

            LoginProfileTextField(
                value = uiState.ageText,
                onValueChange = {},
                labelLeadingIcon = R.drawable.ic_profile_age,
                labelTrailingIcon = R.drawable.ic_arrow_down,
                label = R.string.age,
                placeholder = R.string.age,
                enabled = false,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 20.dp)
                    .noRippleClickable(onClick = { isShownAgeDialog = true }),
            )

            Spacer(modifier = Modifier.height(34.dp))

            AppContinueButton(
                enabled = uiState.isContinueEnabled,
                isLoading = uiState.isLoading,
                onClick = onContinueClick,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(50.dp)
                    .padding(horizontal = 24.dp)
            )
        }
    }

    GenderDialog(
        isShown = isShownGenderDialog,
        defaultGender = uiState.gender,
        onDismissRequest = { isShownGenderDialog = false },
        onGenderConfirmRequest = { onGenderChange(it) }
    )
    GenderDialog(
        isShown = isShownMeetDialog,
        defaultGender = uiState.meet,
        onDismissRequest = { isShownMeetDialog = false },
        onGenderConfirmRequest = { onMeetChange(it) }
    )
}

@Composable
private fun LoginProfileCompleteTopContent(
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight()
    ) {
        Image(
            painter = painterResource(R.mipmap.ic_profile_complete_bg),
            contentDescription = null,
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.TopCenter)
        )

        Image(
            painter = painterResource(R.drawable.ic_logo),
            contentDescription = null,
            modifier = Modifier
                .width(159.dp)
                .height(45.dp)
                .align(Alignment.Center)
        )
    }
}

@Preview
@Composable
private fun LoginProfileCompleteScreenPreview() {
    AppTheme {
        LoginProfileCompleteContent(
            uiState = LoginProfileUIState()
        )
    }
}
