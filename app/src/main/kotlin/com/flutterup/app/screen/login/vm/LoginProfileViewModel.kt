package com.flutterup.app.screen.login.vm

import com.flutterup.app.model.Gender
import com.flutterup.app.screen.login.state.LoginProfileUIState
import com.flutterup.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class LoginProfileViewModel @Inject constructor() : BaseViewModel() {

    private val _uiState = MutableStateFlow(LoginProfileUIState())
    val uiState: StateFlow<LoginProfileUIState> = _uiState.asStateFlow()

    fun updateNickname(nickname: String) {
        _uiState.update { it.copy(nickname = nickname) }
    }

    fun updateGender(gender: Gender) {
        _uiState.update { it.copy(gender = gender) }
    }

    fun updateMeet(gender: Gender) {
        _uiState.update { it.copy(meet = gender) }
    }

    fun updateAge(age: Int) {
        _uiState.update { it.copy(age = age) }
    }

    fun complete() {

    }
}