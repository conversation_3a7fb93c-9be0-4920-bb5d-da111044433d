package com.flutterup.app.screen

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.flutterup.app.R
import com.flutterup.app.design.component.AppBottomSheetDialog
import com.flutterup.app.design.component.AppTextRadioButton
import com.flutterup.app.design.text.BodySize
import com.flutterup.app.design.text.BodyText
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.TextBlack
import com.flutterup.app.model.Gender

@Composable
fun GenderDialog(
    isShown: Boolean,
    defaultGender: Gender = Gender.UNSPECIFIED,
    options: List<Gender> = Gender.options,
    onDismissRequest: () -> Unit,
    onGenderConfirmRequest: (Gender) -> Unit,
) {
    GenderContent(
        isShown = isShown,
        defaultGender = defaultGender,
        options = options,
        onDismissRequest = onDismissRequest,
        onGenderConfirmRequest = onGenderConfirmRequest,
    )
}

@Composable
private fun GenderContent(
    isShown: Boolean,
    defaultGender: Gender,
    options: List<Gender>,
    onDismissRequest: () -> Unit,
    onGenderConfirmRequest: (Gender) -> Unit,
) {
    var selectedGender by remember { mutableStateOf(defaultGender) }

    AppBottomSheetDialog(
        isShown = isShown,
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight(),
        onDismissRequest = onDismissRequest,
        onConfirmRequest = {
            onGenderConfirmRequest(selectedGender)
        },
        containerPaddingValues = PaddingValues(horizontal = 20.dp, vertical = 15.dp),
        dragHandle = {}
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 25.dp, horizontal = 8.dp)
        ) {
            BodyText(
                text = stringResource(R.string.gender_dialog_title),
                size = BodySize.Large,
                bold = true,
                color = TextBlack
            )

            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight(),
            ) {
                items(
                    items = options,
                    key = { it.value }
                ) { gender ->
                    Spacer(modifier = Modifier.height(10.dp))

                    AppTextRadioButton(
                        modifier = Modifier
                            .fillParentMaxWidth()
                            .height(44.dp),
                        selected = selectedGender == gender,
                        text = stringResource(gender.stringResource),
                        onSelectedChange = {
                            selectedGender = gender
                        }
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun GenderDialogPreview() {
    AppTheme {
        GenderDialog(
            isShown = true,
            defaultGender = Gender.FEMALE,
            options = Gender.options,
            onDismissRequest = {},
            onGenderConfirmRequest = {},
        )
    }
}


