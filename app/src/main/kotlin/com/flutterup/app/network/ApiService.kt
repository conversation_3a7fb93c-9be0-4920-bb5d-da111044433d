package com.flutterup.app.network

import com.flutterup.app.model.UserInfo
import com.flutterup.network.BaseResponse
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface ApiService {
    /**
     * Sign in with Email
     */
    @FormUrlEncoded
    @POST("/signin/email")
    suspend fun signInWithEmail(
        @Field("email") email: String,
        @Field("pwd") password: String
    ) : BaseResponse<UserInfo>
}