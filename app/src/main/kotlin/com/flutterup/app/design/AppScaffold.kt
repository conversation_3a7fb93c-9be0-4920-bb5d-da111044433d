@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.design

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarColors
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import com.flutterup.app.R

@Composable
fun AppScaffold(
    title: @Composable () -> Unit,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier,
    canGoBack: Boolean = true,
    navigationIcon: @Composable () -> Unit = { AppDefaultNavigationIcon(canGoBack, onBackClick) },
    colors: TopAppBarColors = TopAppBarDefaults.centerAlignedTopAppBarColors(
        containerColor = MaterialTheme.colorScheme.surfaceContainer,
        titleContentColor = MaterialTheme.colorScheme.onSurface,
        navigationIconContentColor = MaterialTheme.colorScheme.onSurface
    ),
    content: @Composable (paddingValues: PaddingValues) -> Unit
) {
    Scaffold(
        topBar = {
            CenterAlignedTopAppBar(
                title = title,
                navigationIcon = navigationIcon,
                colors = colors
            )
        },
        modifier = modifier
    ) {
        content(it)
    }
}

@Composable
private fun AppDefaultNavigationIcon(canGoBack: Boolean, onBackClick: () -> Unit) {
    if (!canGoBack) return
    IconButton(onClick = onBackClick) {
        Icon(
            imageVector = ImageVector.vectorResource(id = R.drawable.ic_arrow_back),
            contentDescription = null
        )
    }
}

@Preview
@Composable
private fun AppScaffoldPreview() {
    AppScaffold(
        title = { Text("Title") },
        onBackClick = {}
    ) { }
}