package com.flutterup.app.design.theme

import androidx.compose.foundation.text.selection.TextSelectionColors
import androidx.compose.material3.TextFieldColors
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color

@Composable
private fun transparentIndicatorTextFieldColors(): TextFieldColors {
    return TextFieldDefaults.colors(
        errorIndicatorColor = Color.Transparent,
        focusedIndicatorColor = Color.Transparent,
        unfocusedIndicatorColor = Color.Transparent,
        disabledIndicatorColor = Color.Transparent
    )
}

internal val PurpleTextFieldColors: TextFieldColors @Composable get() {
    return transparentIndicatorTextFieldColors().copy(
        errorContainerColor = PurpleTertiaryContainer,
        disabledContainerColor = PurpleTertiaryContainer,
        focusedContainerColor = PurpleTertiaryContainer,
        unfocusedContainerColor = PurpleTertiaryContainer,

        errorLeadingIconColor = PurplePrimary,
        disabledLeadingIconColor = PurplePrimary,
        focusedLeadingIconColor = PurplePrimary,
        unfocusedLeadingIconColor = PurplePrimary,

        errorTrailingIconColor = PurplePrimary,
        disabledTrailingIconColor = PurplePrimary,
        focusedTrailingIconColor = PurplePrimary,
        unfocusedTrailingIconColor = PurplePrimary,

        errorTextColor = PurplePrimary,
        disabledTextColor = PurplePrimary,
        focusedTextColor = PurplePrimary,
        unfocusedTextColor = PurplePrimary,

        errorSupportingTextColor = PurplePrimary,
        disabledSupportingTextColor = PurplePrimary,
        focusedSupportingTextColor = PurplePrimary,
        unfocusedSupportingTextColor = PurplePrimary,

        errorPlaceholderColor = PurplePrimary,
        disabledPlaceholderColor = PurplePrimary,
        focusedPlaceholderColor = PurplePrimary,
        unfocusedPlaceholderColor = PurplePrimary,
    )
}