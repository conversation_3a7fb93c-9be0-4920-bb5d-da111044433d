package com.flutterup.app.design.theme

import androidx.compose.ui.graphics.Color

/**
 * Now in Android colors.
 */
internal val Blue10 = Color(0xFF001F28)
internal val Blue40 = Color(0xFF006780)
internal val Blue90 = Color(0xFFB8EAFF)
internal val DarkPurpleGray10 = Color(0xFF201A1B)
internal val DarkPurpleGray20 = Color(0xFF362F30)
internal val DarkPurpleGray95 = Color(0xFFFAEEEF)
internal val DarkPurpleGray99 = Color(0xFFFCFCFC)
internal val Orange10 = Color(0xFF380D00)
internal val Orange40 = Color(0xFFA23F16)
internal val Orange90 = Color(0xFFFFDBCF)
internal val Purple10 = Color(0xFF36003C)
internal val Purple90 = Color(0xFFFFD6FA)
internal val PurpleGray30 = Color(0xFF4D444C)
internal val PurpleGray90 = Color(0xFFEDDEE8)
internal val Red10 = Color(0xFF410002)
internal val Red40 = Color(0xFFBA1A1A)
internal val Red90 = Color(0xFFFFDAD6)

internal val PurpleCoverColor = Color(0x822B1F6C)

internal val PurplePrimary = Color(0xFF5C39A5)