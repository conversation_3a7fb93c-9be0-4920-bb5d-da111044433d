package com.flutterup.app.design.theme

import androidx.annotation.VisibleForTesting
import androidx.compose.material3.ColorScheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

/**
 * Light default theme color scheme
 */
@VisibleForTesting
val DefaultColorScheme = lightColorScheme(
    primary = Color.White,
    onPrimary = Color.White,
    primaryContainer = Purple90,
    onPrimaryContainer = Purple10,
    secondary = Orange40,
    onSecondary = Color.White,
    secondaryContainer = Orange90,
    onSecondaryContainer = Orange10,
    tertiary = Blue40,
    onTertiary = Color.White,
    tertiaryContainer = Blue90,
    onTertiaryContainer = Blue10,
    error = Red40,
    onError = Red90,
    errorContainer = Red90,
    onErrorContainer = Red10,
    background = DarkPurpleGray99,
    onBackground = DarkPurpleGray10,
    surface = DarkPurpleGray99,
    onSurface = Color.White,
    surfaceVariant = PurpleGray90,
    onSurfaceVariant = PurpleGray30,
    inverseSurface = DarkPurpleGray20,
    inverseOnSurface = DarkPurpleGray95,
    outline = Color.White,
)

val ColorScheme.DefaultGradientColors get() = GradientColors(
    top = inverseOnSurface,
    bottom = primaryContainer,
    container = surface,
)

val ColorScheme.DefaultBackgroundTheme get() = BackgroundTheme(
    color = surface,
    tonalElevation = 2.dp,
)

val ColorScheme.DefaultTintTheme get() = TintTheme(primary)

/**
 * Now in Android theme.
 */
@Composable
fun AppTheme(
    colorScheme: ColorScheme = DefaultColorScheme,
    gradientColors: GradientColors = colorScheme.DefaultGradientColors,
    backgroundTheme: BackgroundTheme = colorScheme.DefaultBackgroundTheme,
    tintTheme: TintTheme = colorScheme.DefaultTintTheme,
    content: @Composable () -> Unit,
) {
    // Composition locals
    CompositionLocalProvider(
        LocalGradientColors provides gradientColors,
        LocalBackgroundTheme provides backgroundTheme,
        LocalTintTheme provides tintTheme,
    ) {
        MaterialTheme(
            colorScheme = colorScheme,
            typography = FlutterUpTypography,
            content = content,
        )
    }
}

