package com.flutterup.app.design.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.flutterup.app.R
import com.flutterup.app.design.text.BodySize
import com.flutterup.app.design.text.BodyText
import com.flutterup.app.design.theme.AppButtonColors
import com.flutterup.app.design.theme.AppTheme

@Composable
fun AppContinueButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    isLoading: Boolean = false,
    enabled: Boolean = true,
) {
    Button(
        modifier = modifier,
        onClick = { if (!isLoading) onClick() }, //loading 时禁用点击
        enabled = enabled,
        shape = RoundedCornerShape(25.dp),
        colors = AppButtonColors
    ) {
        if (!isLoading) {
            BodyText(
                text = stringResource(R.string.continue_text),
                size = BodySize.Large,
                bold = true,
                color = Color.White,
            )
        }

        if (isLoading) {
            CircularProgressIndicator(
                color = Color.White,
                modifier = Modifier.size(20.dp)
            )
        }
    }
}


@Preview
@Composable
private fun AppContinueButtonPreview() {
    AppTheme {
        Column {
            AppContinueButton(
                onClick = {},
                isLoading = true,
                modifier = Modifier.fillMaxWidth().height(50.dp),
            )

            Spacer(modifier = Modifier.height(20.dp))

            AppContinueButton(
                onClick = {},
                modifier = Modifier.fillMaxWidth().height(50.dp),
                enabled = false,
            )
        }
    }
}