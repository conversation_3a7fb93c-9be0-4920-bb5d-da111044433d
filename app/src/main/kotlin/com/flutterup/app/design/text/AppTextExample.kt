package com.flutterup.app.design.text

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

/**
 * AppText 组件使用示例
 * 展示新增的 bold 和 fontType 参数的使用方法
 */
@Composable
fun AppTextExampleScreen() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 基础 AppText 示例
        TitleText(
            text = "AppText 组件优化示例",
            size = TitleSize.Large,
            bold = true,
            textAlign = TextAlign.Center
        )

        // 加粗参数示例
        BodyText(
            text = "这是普通文本",
            size = BodySize.Large
        )
        
        BodyText(
            text = "这是加粗文本",
            size = BodySize.Large,
            bold = true
        )

        // 字体类型示例
        HeadlineText(
            text = "默认字体 (Default)",
            fontType = AppFontFamily.Default
        )
        
        HeadlineText(
            text = "无衬线字体 (SansSerif)",
            fontType = AppFontFamily.SansSerif
        )
        
        HeadlineText(
            text = "衬线字体 (Serif)",
            fontType = AppFontFamily.Serif
        )
        
        HeadlineText(
            text = "等宽字体 (Monospace)",
            fontType = AppFontFamily.Monospace
        )
        
        HeadlineText(
            text = "手写字体 (Cursive)",
            fontType = AppFontFamily.Cursive
        )

        // 组合使用示例
        DisplayText(
            text = "加粗 + 衬线字体",
            size = DisplaySize.Medium,
            bold = true,
            fontType = AppFontFamily.Serif,
            color = MaterialTheme.colorScheme.primary
        )

        // AnnotatedString 示例
        val annotatedText = buildAnnotatedString {
            append("这是一个 ")
            withStyle(style = SpanStyle(fontWeight = FontWeight.Bold)) {
                append("AnnotatedString")
            }
            append(" 示例，支持 ")
            withStyle(style = SpanStyle(color = Color.Red)) {
                append("颜色")
            }
            append(" 和其他样式。")
        }
        
        BodyText(
            text = annotatedText,
            fontType = AppFontFamily.SansSerif,
            onClick = { offset ->
                // 处理点击事件
                println("点击位置: $offset")
            }
        )

        // 标签文本示例
        LabelText(
            text = "标签文本 - 加粗等宽字体",
            bold = true,
            fontType = AppFontFamily.Monospace,
            color = MaterialTheme.colorScheme.secondary
        )

        // 向后兼容性示例 - 仍然可以使用原有的 fontWeight 和 fontFamily 参数
        AppText(
            text = "向后兼容：使用 fontWeight 参数",
            fontWeight = FontWeight.SemiBold,
            color = MaterialTheme.colorScheme.tertiary
        )

        // 优先级示例 - bold 参数优先于 fontWeight
        AppText(
            text = "优先级：bold=true 会覆盖 fontWeight",
            fontWeight = FontWeight.Light,  // 这个会被忽略
            bold = true,  // 这个会生效
            color = MaterialTheme.colorScheme.error
        )
    }
}

@Preview(showBackground = true)
@Composable
fun AppTextExamplePreview() {
    MaterialTheme {
        AppTextExampleScreen()
    }
}
