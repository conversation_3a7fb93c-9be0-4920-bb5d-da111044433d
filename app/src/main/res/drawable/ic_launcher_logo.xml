<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="94dp"
    android:height="111dp"
    android:viewportWidth="94"
    android:viewportHeight="111">
  <path
      android:pathData="M55.71,0L35.11,2.79C22.84,4.45 13.03,13.82 10.81,26L0,85.26L6.53,84.97C12.37,84.72 17.29,80.52 18.47,74.79L28.36,26.38C29.23,22.15 32.58,18.86 36.82,18.08L50.87,15.5L55.71,0Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="39.57"
          android:startY="0.4"
          android:endX="-0.31"
          android:endY="88.6"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#66FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M41.59,49.69L19.89,52.35H19.89C19.89,52.35 19.88,52.35 19.88,52.35C19.87,52.35 19.86,52.35 19.85,52.35C19.82,52.36 19.79,52.36 19.74,52.37C19.64,52.38 19.5,52.4 19.32,52.43C18.95,52.49 18.43,52.58 17.8,52.71C16.54,52.96 14.83,53.38 13.04,54.02C11.25,54.66 9.38,55.53 7.8,56.68C6.65,57.53 5.64,58.53 4.94,59.72C5.02,59.13 5.15,58.38 5.33,57.51C5.8,55.27 6.65,52.24 8.21,49.14C11.33,42.95 17.23,36.46 28.42,35.2L28.42,35.2L44.46,33.54L41.59,49.69Z"
      android:strokeWidth="0.5">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="20.18"
          android:startY="47.23"
          android:endX="45.06"
          android:endY="38.57"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#66FFFFFF"/>
      </gradient>
    </aapt:attr>
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="64"
          android:startY="33"
          android:endX="24.65"
          android:endY="61.21"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#99FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M85.77,88.34C83.64,100.55 73.24,109.6 60.86,110C47.52,110.43 37.18,98.47 39.54,85.33L44.26,59.07C45.4,52.74 50.56,47.93 56.95,47.23L63.01,46.57L55.65,85.92C54.94,89.72 57.67,93.25 61.45,93.6L61.43,93.62L61.9,93.63C65.09,93.7 67.86,91.45 68.45,88.31L74.45,55.96L74.46,55.95C75.47,49.57 80.63,44.65 87.06,43.94L93.63,43.23L85.77,88.34Z"
      android:strokeWidth="0.4">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="93.92"
          android:startY="60.83"
          android:endX="17.59"
          android:endY="101.64"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#66FFFFFF"/>
      </gradient>
    </aapt:attr>
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="66.43"
          android:startY="43"
          android:endX="66.43"
          android:endY="110.21"
          android:type="linear">
        <item android:offset="0" android:color="#00FFFFFF"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
