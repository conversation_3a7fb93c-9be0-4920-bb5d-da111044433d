package com.flutterup.network

import java.io.IOException

class NetworkException(
    val code: Int,
    val url: String? = null,
    val data: String? = null,
    message: String? = null
) : IOException(message) {

    constructor(baseResponse: BaseResponse<*>) : this(
        baseResponse.code,
        null,
        baseResponse.data?.toString(),
        baseResponse.message
    )

    constructor(baseResponse: BaseResponse<*>, url: String?) : this(
        baseResponse.code,
        url,
        baseResponse.data?.toString(),
        baseResponse.message
    )

    override fun toString(): String {
        return "NetworkException(code=$code, url=$url, data=$data, message=${super.message})"
    }
}